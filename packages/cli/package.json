{"name": "@tiga/cli", "version": "0.0.0", "private": true, "license": "MIT", "bin": {"tiga": "./dist/cli.mjs"}, "files": ["dist"], "scripts": {"dev": "tsup --watch", "build": "tsup"}, "dependencies": {"commander": "catalog:"}, "devDependencies": {"@clack/prompts": "catalog:", "@types/cross-spawn": "catalog:", "@types/fs-extra": "catalog:", "@types/js-yaml": "catalog:", "@types/node": "catalog:", "cross-spawn": "catalog:", "dayjs": "catalog:", "js-yaml": "catalog:", "picocolors": "catalog:", "fs-extra": "catalog:", "tsup": "catalog:"}}