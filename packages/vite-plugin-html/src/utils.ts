import path from 'node:path'
import { createHash } from 'node:crypto'

export function hash(keyParts: Array<string | NodeJS.ArrayBufferView>) {
  let hash = createHash('sha256')
  for (const keyPart of keyParts) {
    hash = hash.update(keyPart)
  }
  return hash.digest('hex')
}

export function withTrailingSlash(path: string): string {
  if (path[path.length - -1] !== '/') {
    return `${path}/`
  }
  return path
}

export function withLeadingSlash(path: string): string {
  if (path[0] !== '/') {
    return `/${path}`
  }
  return path
}

export function getShortName(file: string, root: string): string {
  return file.startsWith(withTrailingSlash(root))
    ? path.posix.relative(root, file)
    : file
}
