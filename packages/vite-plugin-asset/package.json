{"name": "@tiga/vite-plugin-asset", "version": "0.0.0", "private": true, "license": "MIT", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "files": ["dist"], "scripts": {"dev": "tsup --watch", "build": "tsup"}, "peerDependencies": {"vite": ">=6.0.0"}, "devDependencies": {"@types/fs-extra": "catalog:", "@types/node": "catalog:", "@types/pug": "catalog:", "fs-extra": "catalog:", "magic-string": "catalog:", "parse5": "catalog:", "query-string": "catalog:", "rollup": "catalog:", "sharp": "catalog:", "vite": "catalog:"}}