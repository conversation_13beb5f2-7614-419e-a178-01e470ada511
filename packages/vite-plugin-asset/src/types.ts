import type { PluginContext } from 'rollup'
import type { ResolvedConfig, Connect } from 'vite'

export interface AssetPlugin {
  filter: RegExp | ((id: string) => boolean)
  handler: (
    id: string,
    viteConfig: ResolvedConfig,
    pluginContext?: PluginContext,
  ) => Promise<string> | string
  middleware?: Connect.NextHandleFunction
}

export interface AssetPluginOptions {
  sources?: Record<string, string[]>
  plugins?: AssetPlugin[]
}
