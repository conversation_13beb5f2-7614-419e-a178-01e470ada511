import MagicString from 'magic-string'
import { parse, type DefaultTreeAdapterMap, type Token } from 'parse5'

export function nodeIsElement(
  node: DefaultTreeAdapterMap['node'],
): node is DefaultTreeAdapterMap['element'] {
  return node.nodeName[0] !== '#'
}

function traverseNodes(
  node: DefaultTreeAdapterMap['node'],
  visitor: (node: DefaultTreeAdapterMap['node']) => void,
) {
  if (node.nodeName === 'template') {
    node = (node as DefaultTreeAdapterMap['template']).content
  }
  visitor(node)
  if (
    nodeIsElement(node) ||
    node.nodeName === '#document' ||
    node.nodeName === '#document-fragment'
  ) {
    node.childNodes.forEach((childNode) => traverseNodes(childNode, visitor))
  }
}

export function traverseHtml(
  html: string,
  visitor: (node: DefaultTreeAdapterMap['node']) => void,
) {
  const ast = parse(html, {
    scriptingEnabled: false,
    sourceCodeLocationInfo: true,
  })
  traverseNodes(ast, visitor)
}

const attrValueStartRE = /=\s*(.)/

export function overwriteAttrValue(
  s: MagicString,
  sourceCodeLocation: Token.Location,
  newValue: string,
): MagicString {
  const srcString = s.slice(
    sourceCodeLocation.startOffset,
    sourceCodeLocation.endOffset,
  )
  const valueStart = attrValueStartRE.exec(srcString)
  if (!valueStart) {
    // overwrite attr value can only be called for a well-defined value
    throw new Error(
      `[vite:html] internal error, failed to overwrite attribute value`,
    )
  }
  const wrapOffset = valueStart[1] === '"' || valueStart[1] === "'" ? 1 : 0
  const valueOffset = valueStart.index! + valueStart[0].length - 1
  s.update(
    sourceCodeLocation.startOffset + valueOffset + wrapOffset,
    sourceCodeLocation.endOffset - wrapOffset,
    newValue,
  )
  return s
}

interface HtmlAssetAttribute {
  key: string
  value: string
  attributes: Record<string, string>
  location: Token.Location
}

/**
 * 给定一个HTML节点和引用资产的属性，查找引用要处理的资产的所有属性
 */
export function getNodeAssetAttributes(
  node: DefaultTreeAdapterMap['element'],
  assetAttributes: Record<string, string[]>,
): HtmlAssetAttribute[] {
  const matched = assetAttributes[node.nodeName]
  if (!matched) return []

  const attributes: Record<string, string> = {}
  for (const attr of node.attrs) {
    attributes[getAttrKey(attr)] = attr.value
  }

  const actions: HtmlAssetAttribute[] = []
  function handleAttributeKey(key: string) {
    const value = attributes[key]
    if (!value) return
    const location = node.sourceCodeLocation!.attrs![key]
    actions.push({ key, value, attributes, location })
  }
  matched?.forEach((key) => handleAttributeKey(key))
  return actions
}

function getAttrKey(attr: Token.Attribute): string {
  return attr.prefix === undefined ? attr.name : `${attr.prefix}:${attr.name}`
}
