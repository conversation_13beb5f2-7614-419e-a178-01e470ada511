import path from 'node:path'
import fse from 'fs-extra'
import sharp from 'sharp'
import queryString from 'query-string'
import { hash, generateImageID } from '../utils'
import { normalizePath } from 'vite'
import type { WebpOptions, JpegOptions, PngOptions, GifOptions } from 'sharp'
import type { ResolvedConfig } from 'vite'
import type { PluginContext } from 'rollup'
import type { AssetPlugin } from '../types'

export interface CacheOptions {
  /**
   * 缓存目录
   * @default '.cache/images'
   */
  dir?: string

  /**
   * 是否启用缓存
   * @default true
   */
  enabled?: boolean
}

export interface AssetImageOptions {
  /**
   * 缓存配置
   */
  cacheOptions?: CacheOptions

  /**
   * 全局 webp 配置
   * @see https://sharp.pixelplumbing.com/api-output/#webp
   */
  webpOptions?: WebpOptions

  /**
   * 全局 jpeg 配置
   * @see https://sharp.pixelplumbing.com/api-output/#jpeg
   */
  jpegOptions?: JpegOptions

  /**
   * 全局 png 配置
   * @see https://sharp.pixelplumbing.com/api-output/#png
   */
  pngOptions?: PngOptions

  /**
   * 全局 gif 配置
   * @see https://sharp.pixelplumbing.com/api-output/#gif
   */
  gifOptions?: GifOptions
}

type ImageInfo = Awaited<ReturnType<typeof resolveImageInfo>>

interface ProcessedImageBuffers {
  originBuffer: Buffer
  webpBuffer: Buffer | null
}

async function resolveImageInfo(id: string, viteConfig: ResolvedConfig) {
  const { url, query } = queryString.parseUrl(id, {
    parseNumbers: true,
  })

  const params = {
    quality: ((query.q || query.quality) as unknown as number) || undefined,
    lossless: 'lossless' in query,
    rawImage: 'raw-image' in query,
  }

  const file = path.join(viteConfig.root, url)

  const imageBuffer = await fse.readFile(file)

  const imageHash = hash([imageBuffer])
  const imageId = generateImageID(params, imageHash)

  const suffix = getSuffix(params)
  const extname = path.extname(url)
  const imageName = path.basename(url, extname)
  const cacheFileName = `${imageName}-${imageId.substring(0, 8)}${extname}`
  const cacheWebpFileName = `${imageName}-${imageId.substring(0, 8)}.webp`

  return {
    originFileName: file,
    cacheFileName,
    cacheWebpFileName,
    displayFileName: `${url.replace(extname, '')}${suffix}${
      params.rawImage ? extname : '.webp'
    }`,
    outputFileName: `${imageName}${suffix}${extname}`,
    outputWebpFileName: `${imageName}${suffix}.webp`,
    imageBuffer,
    params,
  }
}

function getSuffix(params: Record<string, unknown>) {
  if (params.rawImage) {
    return ''
  }
  let suffix = ''

  if (params.quality) {
    suffix += `-q_${params.quality}`
  }
  if (params.lossless) {
    suffix += `-lossless`
  }
  return suffix
}

async function getProcessedImageBuffers(
  imageInfo: ImageInfo,
  options: AssetImageOptions,
  cachePath: string,
): Promise<ProcessedImageBuffers> {
  const {
    params,
    cacheFileName,
    cacheWebpFileName,
    originFileName,
    imageBuffer,
  } = imageInfo

  if (params.rawImage) {
    return { originBuffer: imageBuffer, webpBuffer: null }
  }

  const cachedOriginPath = path.join(cachePath, cacheFileName)
  const cachedWebpPath = path.join(cachePath, cacheWebpFileName)

  if (await fse.pathExists(cachedWebpPath)) {
    const originBuffer = await fse.readFile(cachedOriginPath)
    const webpBuffer = await fse.readFile(cachedWebpPath)
    return { originBuffer, webpBuffer }
  }

  const image = sharp(originFileName, { animated: true })
  const ext = path.extname(originFileName).toLowerCase()

  let originBufferProcessed: Buffer
  const imageClone = image.clone()

  switch (ext) {
    case '.jpeg':
    case '.jpg':
      originBufferProcessed = await imageClone
        .jpeg(options.jpegOptions)
        .toBuffer()
      break
    case '.png':
      originBufferProcessed = await imageClone
        .png(options.pngOptions)
        .toBuffer()
      break
    case '.gif':
      originBufferProcessed = await imageClone
        .gif(options.gifOptions)
        .toBuffer()
      break
    default:
      originBufferProcessed = await imageClone.toBuffer()
      break
  }

  const webpBuffer = await image
    .clone()
    .webp({
      ...options.webpOptions,
      quality: params.quality ?? options.webpOptions?.quality,
      lossless: params.lossless ?? options.webpOptions?.lossless,
    })
    .toBuffer()

  await fse.writeFile(cachedWebpPath, webpBuffer)
  await fse.writeFile(cachedOriginPath, originBufferProcessed)

  return { originBuffer: originBufferProcessed, webpBuffer }
}

function emitBuildAssets(
  pluginContext: PluginContext,
  viteConfig: ResolvedConfig,
  imageInfo: ImageInfo,
  buffers: ProcessedImageBuffers,
) {
  const {
    params,
    outputFileName,
    outputWebpFileName,
    displayFileName,
    cacheFileName,
    cacheWebpFileName,
  } = imageInfo
  const { originBuffer, webpBuffer } = buffers

  let referenceId = ''

  if (params.rawImage) {
    referenceId = pluginContext.emitFile({
      type: 'asset',
      name: outputFileName,
      originalFileName: normalizePath(
        path.relative(viteConfig.root, outputFileName),
      ),
      source: originBuffer,
    })
  } else if (webpBuffer) {
    // Emit original image type for fallback
    pluginContext.emitFile({
      type: 'asset',
      name: outputFileName,
      originalFileName: normalizePath(
        path.relative(viteConfig.root, cacheFileName),
      ),
      source: originBuffer,
    })
    // Emit webp version and return its referenceId
    referenceId = pluginContext.emitFile({
      type: 'asset',
      name: outputWebpFileName,
      originalFileName: normalizePath(
        path.relative(viteConfig.root, cacheWebpFileName),
      ),
      source: webpBuffer,
    })
  } else {
    // Fallback for non-webp images if ever needed
    referenceId = pluginContext.emitFile({
      type: 'asset',
      name: outputFileName,
      originalFileName: displayFileName,
      source: originBuffer,
    })
  }

  return referenceId
}

export function imagePlugin(options: AssetImageOptions = {}): AssetPlugin {
  const { cacheOptions = { enabled: true, dir: '.cache/images' } } = options

  const pwd = process.cwd()
  const cachePath = path.join(pwd, cacheOptions.dir!)
  fse.ensureDirSync(cachePath)

  const assetCache = new Map<string, string>()

  return {
    filter: /\.(png|jpg|jpeg|webp|gif)(\?.*)?$/,
    async handler(id, viteConfig, pluginContext) {
      const cached = assetCache.get(id)

      if (cached) {
        return cached
      }

      const imageInfo = await resolveImageInfo(id, viteConfig)

      const { originFileName, cacheWebpFileName, params, displayFileName } =
        imageInfo

      const buffers = await getProcessedImageBuffers(
        imageInfo,
        options,
        cachePath,
      )

      let assetUrl = displayFileName

      if (pluginContext) {
        const referenceId = emitBuildAssets(
          pluginContext,
          viteConfig,
          imageInfo,
          buffers,
        )

        assetUrl = `__VITE_ASSET__${referenceId}__`
      }

      assetCache.set(displayFileName, assetUrl)

      return assetUrl
    },

    middleware(req, res, next) {
      next()
    },
  }
}
