{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tiga dev", "build:preview": "tiga build:preview", "build:patch": "tiga build:patch", "build:complete": "tiga build:complete", "gen:env": "tiga gen:env", "preview": "vite preview", "lint": "eslint \"src/**/*.ts\"", "test": "vitest"}, "devDependencies": {"@prettier/plugin-pug": "catalog:", "@tiga/postcss-rpx2calc": "workspace:*", "@tiga/cli": "workspace:*", "@tiga/vite-plugin-asset": "workspace:*", "@tiga/vite-plugin-build": "workspace:*", "@tiga/vite-plugin-html": "workspace:*", "autoprefixer": "catalog:", "eslint": "catalog:", "postcss": "catalog:", "prettier": "catalog:", "sass-embedded": "catalog:", "unocss": "catalog:", "vite": "catalog:"}}