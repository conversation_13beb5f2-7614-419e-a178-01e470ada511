import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import { htmlPlugin } from '@tiga/vite-plugin-html'
import { assetPlugin, imagePlugin } from '@tiga/vite-plugin-asset'
import rpx2CalcPlugin from '@tiga/postcss-rpx2calc'
import buildPlugin from '@tiga/vite-plugin-build'
import autoprefixer from 'autoprefixer'
import UnoCSS from 'unocss/vite'


export default defineConfig({
  base: './',
  css: {
    postcss: {
      plugins: [autoprefixer(), rpx2CalcPlugin()],
    },
  },
  plugins: [
    htmlPlugin(),
    assetPlugin({
      sources: {
        img: ['src', 'data-src', 'data-src-mo', 'data-src-pc', 'data-src-pad'],
        video: ['src', 'poster', 'data-src'],
      },
      plugins: [imagePlugin()],
    }),
    UnoCSS(),
    // buildPlugin(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '~images': fileURLToPath(new URL('./src/assets/images', import.meta.url)),
      '~videos': fileURLToPath(new URL('./src/assets/videos', import.meta.url)),
    },
  },
  define: {
    env: JSON.parse(process.env.__INJECT_ENVS__ || '{}'),
  },
  build: {
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      output: {
        format: 'iife',
        entryFileNames: '[name].js',
      },
    },
  },
})
