import './styles/index.scss'
import 'virtual:uno.css'

async function main() {
  const imgs = document.querySelectorAll<HTMLImageElement>(
    '.zn--wrapper .lazy-img',
  )

  // @ts-ignore
  const { default: url } = await import('~images/sticker.png')
  console.log(url)

  if (imgs) {
    imgs.forEach((img) => {
      img.src = img.getAttribute('data-src') || ''
      img.decode().then(() => {
        console.log('decoded')
      })
    })
  }
}

main()

console.log(env)
